'use client';

import { motion } from 'framer-motion';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import {
  Calendar,
  User,
  ArrowRight,
  Cpu,
  Utensils,
  Car,
  Hash,
  Palette,
  Zap,
  Globe,
  Brain,
  FileText,
  Code,
  Wrench
} from 'lucide-react';
import Link from 'next/link';
import { useTheme } from '@/hooks/useTheme';
import { useCategoryName } from '@/lib/categoryUtils';

interface BlogPost {
  id: string;
  _id: string;
  title: string;
  excerpt: string;
  slug: string;
  featuredImage?: string;
  imageCredit?: string;
  categoryId?: string;
  tags: string[];
  publishedAt: string;
  author: {
    name: string;
    email: string;
  };
}

interface BlogCardProps {
  post: BlogPost;
  index: number;
}

export function BlogCard({ post, index }: BlogCardProps) {
  const { theme } = useTheme();
  const isDark = theme === 'dark';

  // Get the proper category name
  const categoryName = useCategoryName(post.categoryId || 'General');

  // Enhanced category configuration with icons and colors
  const getCategoryConfig = (category: string) => {
    const configs = {
      'Technology': {
        icon: Cpu,
        colors: isDark
          ? 'bg-gradient-to-r from-blue-500/20 to-indigo-500/20 text-blue-300 border-blue-500/40 shadow-blue-500/20'
          : 'bg-gradient-to-r from-blue-50 to-indigo-50 text-blue-800 border-blue-300 shadow-blue-200/50'
      },
      'Food': {
        icon: Utensils,
        colors: isDark
          ? 'bg-gradient-to-r from-green-500/20 to-emerald-500/20 text-green-300 border-green-500/40 shadow-green-500/20'
          : 'bg-gradient-to-r from-green-50 to-emerald-50 text-green-800 border-green-300 shadow-green-200/50'
      },
      'Automotive': {
        icon: Car,
        colors: isDark
          ? 'bg-gradient-to-r from-purple-500/20 to-violet-500/20 text-purple-300 border-purple-500/40 shadow-purple-500/20'
          : 'bg-gradient-to-r from-purple-50 to-violet-50 text-purple-800 border-purple-300 shadow-purple-200/50'
      },
      'General': {
        icon: Hash,
        colors: isDark
          ? 'bg-gradient-to-r from-orange-500/20 to-amber-500/20 text-orange-300 border-orange-500/40 shadow-orange-500/20'
          : 'bg-gradient-to-r from-orange-50 to-amber-50 text-orange-800 border-orange-300 shadow-orange-200/50'
      },
      'Design': {
        icon: Palette,
        colors: isDark
          ? 'bg-gradient-to-r from-violet-500/20 to-fuchsia-500/20 text-violet-300 border-violet-500/40 shadow-violet-500/20'
          : 'bg-gradient-to-r from-violet-50 to-fuchsia-50 text-violet-800 border-violet-300 shadow-violet-200/50'
      },
      'Productivity': {
        icon: Zap,
        colors: isDark
          ? 'bg-gradient-to-r from-emerald-500/20 to-teal-500/20 text-emerald-300 border-emerald-500/40 shadow-emerald-500/20'
          : 'bg-gradient-to-r from-emerald-50 to-teal-50 text-emerald-800 border-emerald-300 shadow-emerald-200/50'
      },
      'Web': {
        icon: Globe,
        colors: isDark
          ? 'bg-gradient-to-r from-cyan-500/20 to-sky-500/20 text-cyan-300 border-cyan-500/40 shadow-cyan-500/20'
          : 'bg-gradient-to-r from-cyan-50 to-sky-50 text-cyan-800 border-cyan-300 shadow-cyan-200/50'
      },
      'AI': {
        icon: Brain,
        colors: isDark
          ? 'bg-gradient-to-r from-pink-500/20 to-rose-500/20 text-pink-300 border-pink-500/40 shadow-pink-500/20'
          : 'bg-gradient-to-r from-pink-50 to-rose-50 text-pink-800 border-pink-300 shadow-pink-200/50'
      },
      'PDF Tools': {
        icon: FileText,
        colors: isDark
          ? 'bg-gradient-to-r from-red-500/20 to-orange-500/20 text-red-300 border-red-500/40 shadow-red-500/20'
          : 'bg-gradient-to-r from-red-50 to-orange-50 text-red-800 border-red-300 shadow-red-200/50'
      },
      'Development': {
        icon: Code,
        colors: isDark
          ? 'bg-gradient-to-r from-slate-500/20 to-gray-500/20 text-slate-300 border-slate-500/40 shadow-slate-500/20'
          : 'bg-gradient-to-r from-slate-50 to-gray-50 text-slate-800 border-slate-300 shadow-slate-200/50'
      },
      'Tools': {
        icon: Wrench,
        colors: isDark
          ? 'bg-gradient-to-r from-yellow-500/20 to-orange-500/20 text-yellow-300 border-yellow-500/40 shadow-yellow-500/20'
          : 'bg-gradient-to-r from-yellow-50 to-orange-50 text-yellow-800 border-yellow-300 shadow-yellow-200/50'
      }
    };

    return configs[category as keyof typeof configs] || {
      icon: Hash,
      colors: isDark
        ? 'bg-gradient-to-r from-gray-500/20 to-slate-500/20 text-gray-300 border-gray-500/40 shadow-gray-500/20'
        : 'bg-gradient-to-r from-gray-50 to-slate-50 text-gray-800 border-gray-300 shadow-gray-200/50'
    };
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const categoryConfig = getCategoryConfig(categoryName);
  const CategoryIcon = categoryConfig.icon;

  return (
    <motion.div
      initial={{ opacity: 0, y: 40, scale: 0.85, rotateX: 15 }}
      animate={{ opacity: 1, y: 0, scale: 1, rotateX: 0 }}
      transition={{
        duration: 0.8,
        delay: index * 0.15,
        ease: [0.25, 0.46, 0.45, 0.94],
        type: "spring",
        stiffness: 120,
        damping: 20
      }}
      whileHover={{
        y: -4,
        boxShadow: "0 10px 25px rgba(0,0,0,0.1)",
        transition: {
          duration: 0.4,
          ease: [0.25, 0.46, 0.45, 0.94],
          type: "spring",
          stiffness: 300
        }
      }}
      whileTap={{ scale: 0.97, y: -2 }}
      className="group h-full max-w-[350px] mx-auto perspective-1000"
    >
      <motion.div
        className={`
          h-full rounded-3xl overflow-hidden shadow-xl transition-all duration-700
          group-hover:shadow-2xl transform-gpu
          ${isDark
            ? 'bg-gradient-to-br from-gray-900/95 via-gray-800/95 to-gray-900/95 border border-gray-700/40 group-hover:border-gray-600/60 backdrop-blur-lg'
            : 'bg-gradient-to-br from-white/95 via-gray-50/80 to-white/95 border border-gray-200/60 group-hover:border-gray-300/80 backdrop-blur-lg'
          }
          group-hover:shadow-primary/20
        `}
        whileHover={{
          boxShadow: isDark
            ? "0 25px 50px -12px rgba(0, 0, 0, 0.8), 0 0 0 1px rgba(255, 255, 255, 0.1)"
            : "0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)"
        }}
      >
        {/* Enhanced Image Section */}
        <div className="relative h-52 overflow-hidden rounded-t-3xl">
          <motion.div
            className="absolute inset-0 bg-gradient-to-br from-primary/20 via-transparent to-secondary/20 opacity-0 group-hover:opacity-100 transition-all duration-700 z-10"
          />
          <motion.img
            src={post.featuredImage || 'https://images.unsplash.com/photo-1486312338219-ce68d2c6f44d?w=800&q=80'}
            alt={post.title}
            className="w-full h-full object-cover transition-all duration-700 group-hover:scale-115 group-hover:brightness-110 group-hover:contrast-110"
            whileHover={{
              scale: 1.08,
              filter: "brightness(1.1) contrast(1.1) saturate(1.2)"
            }}
            transition={{ duration: 0.6, ease: "easeOut" }}
          />

          {/* Enhanced Category Badge with Icon */}
          <motion.div
            className="absolute top-4 left-4 z-10"
            initial={{ opacity: 0, scale: 0.8, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            transition={{ delay: 0.3, duration: 0.5, ease: "easeOut" }}
            whileHover={{
              scale: 1.08,
              y: -2,
              transition: { duration: 0.2, ease: "easeOut" }
            }}
            whileTap={{ scale: 0.95 }}
          >
            <Badge
              className={`
                ${categoryConfig.colors}
                border-2 font-bold px-4 py-2.5 text-xs rounded-full shadow-xl backdrop-blur-xl
                transition-all duration-500 hover:shadow-2xl transform-gpu
                flex items-center gap-2 min-w-fit
                hover:scale-105 active:scale-95
              `}
            >
              <motion.div
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.6, ease: "easeInOut" }}
              >
                <CategoryIcon className="h-3.5 w-3.5" />
              </motion.div>
              <span className="font-semibold tracking-wide">
                {categoryName}
              </span>
            </Badge>
          </motion.div>

          {/* Enhanced Image Credit */}
          {post.imageCredit && (
            <motion.div
              className="absolute bottom-3 right-3"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.5 }}
            >
              <span className="text-xs text-white/80 bg-black/60 px-3 py-1.5 rounded-full backdrop-blur-md font-medium">
                📸 {post.imageCredit}
              </span>
            </motion.div>
          )}

          {/* Enhanced Gradient Overlay */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-t from-black/40 via-black/10 to-transparent opacity-0 group-hover:opacity-100 transition-all duration-700 z-20"
            initial={{ opacity: 0 }}
            whileHover={{ opacity: 1 }}
          />

          {/* Enhanced Shimmer Effect */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent opacity-0 group-hover:opacity-100 z-30"
            animate={{
              x: ['-100%', '100%'],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              repeatDelay: 4,
              ease: "easeInOut"
            }}
          />

          {/* Floating Particles Effect */}
          <motion.div
            className="absolute inset-0 opacity-0 group-hover:opacity-100 transition-all duration-1000 z-20"
            initial={{ opacity: 0 }}
            whileHover={{ opacity: 1 }}
          >
            {[...Array(6)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-1 h-1 bg-white/60 rounded-full"
                style={{
                  left: `${20 + i * 15}%`,
                  top: `${30 + (i % 2) * 40}%`,
                }}
                animate={{
                  y: [0, -20, 0],
                  opacity: [0.3, 1, 0.3],
                  scale: [0.5, 1, 0.5],
                }}
                transition={{
                  duration: 2 + i * 0.5,
                  repeat: Infinity,
                  delay: i * 0.3,
                  ease: "easeInOut"
                }}
              />
            ))}
          </motion.div>
        </div>

        {/* Enhanced Content Section */}
        <div className="p-7 flex flex-col h-[calc(100%-13rem)] relative">
          {/* Background Glow Effect */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-secondary/5 opacity-0 group-hover:opacity-100 transition-all duration-700 rounded-b-3xl"
            initial={{ opacity: 0 }}
            whileHover={{ opacity: 1 }}
          />

          {/* Enhanced Meta Information */}
          <motion.div
            className="flex items-center gap-4 text-sm text-muted-foreground mb-5 relative z-10"
            initial={{ opacity: 0, y: 15 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4, duration: 0.6, ease: "easeOut" }}
          >
            <motion.div
              className="flex items-center gap-2"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <motion.div
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.5 }}
              >
                <Calendar className="h-4 w-4 text-primary/70" />
              </motion.div>
              <span className="font-semibold">{formatDate(post.publishedAt)}</span>
            </motion.div>
            <motion.div
              className="w-1.5 h-1.5 bg-primary/50 rounded-full"
              animate={{ scale: [1, 1.2, 1] }}
              transition={{ duration: 2, repeat: Infinity }}
            />
            <motion.div
              className="flex items-center gap-2"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <motion.div
                whileHover={{ rotate: 360 }}
                transition={{ duration: 0.5 }}
              >
                <User className="h-4 w-4 text-primary/70" />
              </motion.div>
              <span className="font-semibold">{post.author.name}</span>
            </motion.div>
          </motion.div>

          {/* Enhanced Title */}
          <motion.h3
            className={`
              text-xl font-bold mb-5 line-clamp-2 group-hover:text-primary transition-all duration-500 relative z-10
              ${isDark ? 'text-gray-100' : 'text-gray-900'}
              leading-tight cursor-pointer
            `}
            initial={{ opacity: 0, y: 15 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.5, duration: 0.6, ease: "easeOut" }}
            whileHover={{
              scale: 1.02,
              transition: { duration: 0.2 }
            }}
          >
            <motion.span
              className="bg-gradient-to-r from-current via-primary to-current bg-clip-text"
              whileHover={{
                backgroundSize: "200% 100%",
                backgroundPosition: "100% 0%"
              }}
              transition={{ duration: 0.5 }}
            >
              {post.title}
            </motion.span>
          </motion.h3>

          {/* Enhanced Excerpt */}
          <motion.p
            className={`
              text-sm mb-6 line-clamp-3 flex-grow leading-relaxed relative z-10
              ${isDark ? 'text-gray-300 group-hover:text-gray-200' : 'text-gray-600 group-hover:text-gray-700'}
              transition-all duration-500
            `}
            initial={{ opacity: 0, y: 15 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.6, duration: 0.6, ease: "easeOut" }}
            whileHover={{
              scale: 1.01,
              transition: { duration: 0.2 }
            }}
          >
            {post.excerpt}
          </motion.p>

          {/* Enhanced Read More Button */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7, duration: 0.6, ease: "easeOut" }}
            className="relative z-10"
          >
            <Link href={`/blog/${post.slug}`}>
              <motion.div
                whileHover={{ scale: 1.03, y: -2 }}
                whileTap={{ scale: 0.97, y: 0 }}
                className="w-full"
              >
                <Button
                  variant="ghost"
                  className={`
                    w-full group/btn justify-between px-6 py-4 rounded-2xl font-bold text-sm
                    transition-all duration-500 hover:shadow-xl relative overflow-hidden
                    ${isDark
                      ? 'text-gray-300 hover:text-white hover:bg-gradient-to-r hover:from-gray-800/60 hover:to-gray-700/60 border-2 border-gray-700/60 hover:border-primary/50'
                      : 'text-gray-700 hover:text-gray-900 hover:bg-gradient-to-r hover:from-gray-100/80 hover:to-gray-50/80 border-2 border-gray-200/60 hover:border-primary/50'
                    }
                    transform-gpu backdrop-blur-sm
                  `}
                >
                  {/* Button Background Glow */}
                  <motion.div
                    className="absolute inset-0 bg-gradient-to-r from-primary/10 via-secondary/10 to-primary/10 opacity-0 group-hover/btn:opacity-100 transition-all duration-500"
                    initial={{ x: '-100%' }}
                    whileHover={{ x: '100%' }}
                    transition={{ duration: 0.8, ease: "easeInOut" }}
                  />

                  <span className="relative z-10 tracking-wide">Read More</span>
                  <motion.div
                    className="relative z-10"
                    whileHover={{ x: 8, rotate: 15 }}
                    transition={{ duration: 0.3, ease: "easeOut" }}
                  >
                    <ArrowRight className="h-4 w-4" />
                  </motion.div>
                </Button>
              </motion.div>
            </Link>
          </motion.div>
        </div>
      </motion.div>

    </motion.div>
  );
}


