"use client";

import { useState, useEffect, use<PERSON>allback } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { DateTimePicker } from "@/components/ui/date-time-picker";
import { Badge } from "@/components/ui/badge";
import { X, Plus, Tag as TagIcon, Calendar, Info, Image as ImageIcon } from "lucide-react";
import { CategoryDropdown } from "@/components/blog/CategoryDropdown";
import { ImageUploader } from "@/components/admin/ImageUploader";

// Mock tag suggestions for demo
const SUGGESTED_TAGS = [
  "javascript",
  "react",
  "nextjs",
  "typescript",
  "webdev",
  "frontend",
  "backend",
  "fullstack",
  "design",
  "ui",
  "ux",
  "css",
  "html",
  "tailwind",
  "programming",
  "coding",
  "tutorial",
  "guide",
  "tips",
  "tricks"
];

interface EditorSidebarProps {
  description: string;
  setDescription: (value: string) => void;
  tags: string[];
  setTags: (value: string[]) => void;
  status: string;
  setStatus: (value: string) => void;
  visibility?: string;
  setVisibility?: (value: string) => void;
  scheduledAt: Date | null;
  setScheduledAt: (value: Date | null) => void;
  categoryId?: string;
  setCategoryId?: (value: string) => void;
  featuredImage?: string;
  setFeaturedImage?: (value: string) => void;
  credit?: string;
  setCredit?: (value: string) => void;
}

export function EditorSidebar({
  description,
  setDescription,
  tags,
  setTags,
  status,
  setStatus,
  visibility = 'public',
  setVisibility = () => {},
  scheduledAt,
  setScheduledAt,
  categoryId = '',
  setCategoryId = () => {},
  featuredImage = '',
  setFeaturedImage = () => {},
  credit = '',
  setCredit = () => {},
}: EditorSidebarProps) {
  const [newTag, setNewTag] = useState("");
  const [isScheduled, setIsScheduled] = useState(status === "scheduled");
  const [tagSuggestions, setTagSuggestions] = useState<string[]>([]);




  // Update tag suggestions when input changes
  useEffect(() => {
    if (newTag.trim() === "") {
      setTagSuggestions([]);
      return;
    }

    const filteredSuggestions = SUGGESTED_TAGS
      .filter(tag =>
        tag.toLowerCase().includes(newTag.toLowerCase()) &&
        !tags.includes(tag)
      )
      .slice(0, 5);

    setTagSuggestions(filteredSuggestions);
  }, [newTag, tags]);



  // Handle adding a new tag
  const handleAddTag = () => {
    if (newTag && !tags.includes(newTag)) {
      setTags([...tags, newTag]);
      setNewTag("");
      setTagSuggestions([]);
    }
  };

  // Handle adding a suggested tag
  const handleAddSuggestedTag = (tag: string) => {
    if (!tags.includes(tag)) {
      setTags([...tags, tag]);
      setNewTag("");
      setTagSuggestions([]);
    }
  };

  // Handle removing a tag
  const handleRemoveTag = (tagToRemove: string) => {
    setTags(tags.filter((tag) => tag !== tagToRemove));
  };





  // Handle status change
  const handleStatusChange = (value: string) => {
    setStatus(value);
    setIsScheduled(value === "scheduled");
  };

  return (
    <div className="space-y-6">
      {/* Publish Settings */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between py-4">
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-4 w-4 text-primary" />
            Publish Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="status">Status</Label>
            <div className="flex items-center gap-2">
              <div className={`h-2 w-2 rounded-full ${
                status === 'draft' ? 'bg-yellow-500' :
                status === 'published' ? 'bg-green-500' :
                status === 'scheduled' ? 'bg-blue-500' : 'bg-gray-500'
              }`} />
              <Select
                value={status || "draft"}
                onValueChange={useCallback((value: string) => {
                  handleStatusChange(value);
                }, [handleStatusChange])}
              >
                <SelectTrigger className="flex-1">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="draft">Draft</SelectItem>
                  <SelectItem value="published">Published</SelectItem>
                  <SelectItem value="scheduled">Scheduled</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <p className="text-xs text-muted-foreground">
              {status === 'draft' ? 'Post is not visible to the public' :
               status === 'published' ? 'Post is live and visible to the public' :
               status === 'scheduled' ? 'Post will be published automatically at the scheduled time' : ''}
            </p>
          </div>

          {isScheduled && (
            <div className="space-y-2">
              <Label htmlFor="scheduledAt">Schedule Date</Label>
              <DateTimePicker
                value={scheduledAt}
                onChange={setScheduledAt}
              />
              <p className="text-xs text-muted-foreground">
                {scheduledAt ? `Will be published on ${scheduledAt.toLocaleDateString()} at ${scheduledAt.toLocaleTimeString()}` : 'Select a date and time to schedule publication'}
              </p>
            </div>
          )}

          <div className="space-y-2">
            <Label htmlFor="visibility">Visibility</Label>
            <Select
              value={visibility || "public"}
              onValueChange={useCallback((value: string) => {
                setVisibility(value);
              }, [setVisibility])}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select visibility" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="public">Public</SelectItem>
                <SelectItem value="private">Private</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
              </SelectContent>
            </Select>
            <p className="text-xs text-muted-foreground">
              {visibility === 'public' ? 'Visible to everyone' :
               visibility === 'private' ? 'Only visible to logged-in users with access' :
               'Only visible to you and editors'}
            </p>
          </div>


        </CardContent>
      </Card>

      {/* SEO Settings */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between py-4">
          <CardTitle className="flex items-center gap-2">
            <Info className="h-4 w-4 text-primary" />
            SEO Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <Label htmlFor="description">Search Description</Label>
              <span className="text-xs text-muted-foreground">
                {description.length} / 160 characters
              </span>
            </div>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value.slice(0, 160))}
              placeholder="Brief description for search results"
              className="min-h-[100px]"
              maxLength={160}
            />
          </div>
        </CardContent>
      </Card>
      {/* Featured Image */}
      {/* <Card>
        <CardHeader className="flex flex-row items-center justify-between py-4">
          <CardTitle className="flex items-center gap-2">
            <ImageIcon className="h-4 w-4 text-primary" />
            Featured Image
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="featuredImage">Upload Featured Image</Label>
            <ImageUploader
              label="Upload Featured Image"
              type="image"
              currentImageUrl={featuredImage}
              onImageUpload={setFeaturedImage}
            />
            <p className="text-xs text-muted-foreground">
              This image will be displayed as the main image for your blog post.
            </p>
          </div>
        </CardContent>
      </Card> */}

      {/* Credit */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between py-4">
          <CardTitle className="flex items-center gap-2">
            <Info className="h-4 w-4 text-primary" />
            Credit
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="credit">Author or Website</Label>
            <Input
              id="credit"
              value={credit}
              onChange={(e) => setCredit(e.target.value)}
              placeholder="Author name or website URL"
              maxLength={200}
            />
            <p className="text-xs text-muted-foreground">
              Give credit to the author or source website.
            </p>
          </div>
        </CardContent>
      </Card>





      {/* Category */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between py-4">
          <CardTitle className="flex items-center gap-2">
            <TagIcon className="h-4 w-4 text-primary" />
            Category
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="category">Select or create category</Label>
            <CategoryDropdown
              selectedCategoryId={categoryId}
              onCategoryChange={setCategoryId}
              placeholder="Select category for your post"
            />
            <p className="text-xs text-muted-foreground mt-2">
              You can select an existing category or create a new one.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Tags */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between py-4">
          <CardTitle className="flex items-center gap-2">
            <TagIcon className="h-4 w-4 text-primary" />
            Tags
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-2">
            {tags.map((tag) => (
              <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                {tag}
                <X
                  className="h-3 w-3 cursor-pointer"
                  onClick={() => handleRemoveTag(tag)}
                />
              </Badge>
            ))}
            {tags.length === 0 && (
              <p className="text-sm text-muted-foreground">No tags added</p>
            )}
          </div>

          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <Input
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Add a tag"
                onKeyDown={(e) => {
                  if (e.key === "Enter") {
                    e.preventDefault();
                    handleAddTag();
                  }
                }}
              />
              <Button
                type="button"
                size="icon"
                variant="outline"
                onClick={handleAddTag}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            {tagSuggestions.length > 0 && (
              <div className="bg-background border border-input rounded-md p-2 space-y-1">
                <p className="text-xs text-muted-foreground mb-2">Suggestions:</p>
                <div className="flex flex-wrap gap-1">
                  {tagSuggestions.map((suggestion) => (
                    <Badge
                      key={suggestion}
                      variant="secondary"
                      className="cursor-pointer hover:bg-primary hover:text-primary-foreground transition-colors"
                      onClick={() => handleAddSuggestedTag(suggestion)}
                    >
                      {suggestion}
                    </Badge>
                  ))}
                </div>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
